const axios = require('axios');

// 测试服务器功能
const BASE_URL = 'http://localhost:3000';
const AUTH_TOKEN = 'a881018';

async function testModelsAPI() {
  try {
    console.log('Testing /v1/models endpoint...');
    const response = await axios.get(`${BASE_URL}/gateway/v1/models`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`
      }
    });
    
    console.log('Models API Response Status:', response.status);
    console.log('Available models count:', response.data.data.length);
    console.log('First few models:', response.data.data.slice(0, 3));
    return true;
  } catch (error) {
    console.error('Models API Error:', error.response?.data || error.message);
    return false;
  }
}

async function testChatAPI() {
  try {
    console.log('\nTesting /v1/chat/completions endpoint...');
    const response = await axios.post(`${BASE_URL}/gateway/v1/chat/completions`, {
      model: 'gpt-4o-mini',
      messages: [
        { role: 'user', content: 'Hello! Please respond with just "Test successful"' }
      ],
      max_tokens: 50,
      stream: false
    }, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('Chat API Response Status:', response.status);
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.error('Chat API Error:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('Starting server tests...\n');
  
  const modelsTest = await testModelsAPI();
  const chatTest = await testChatAPI();
  
  console.log('\n=== Test Results ===');
  console.log('Models API:', modelsTest ? '✅ PASS' : '❌ FAIL');
  console.log('Chat API:', chatTest ? '✅ PASS' : '❌ FAIL');
  
  if (modelsTest && chatTest) {
    console.log('\n🎉 All tests passed! Server is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the server logs.');
  }
}

// 等待服务器启动
setTimeout(runTests, 2000);
