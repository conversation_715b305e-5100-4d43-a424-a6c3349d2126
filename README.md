# Gateway Server - Simplified Version

这是一个简化版本的 LLM 网关服务器，只保留了最初的核心功能，去除了所有复杂的附加功能。

## 功能特性

### 核心功能
- ✅ 根据用户输入的模型名称从配置文件读取配置
- ✅ 向后端 `https://gateway.588886.xyz` 转发请求
- ✅ 实时转发后端响应消息
- ✅ 支持多个网关路径（gateway, test, xdk 等）
- ✅ 模型名称映射（modelsmap）
- ✅ 配置文件完全兼容最新版本
- ✅ Bearer token 认证
- ✅ 模型列表 API (`/v1/models`)
- ✅ 聊天完成 API (`/v1/chat/completions`)

### 移除的功能
- ❌ 搜索功能（search mode）
- ❌ 思考模式（thinking mode）
- ❌ R1 模式处理
- ❌ 内容转换器（transformers）
- ❌ 文件上传处理
- ❌ 复杂的流式处理心跳
- ❌ 日志记录
- ❌ 函数调用转换
- ❌ Gemini 特殊处理
- ❌ Grok 消息处理

## 文件结构

```
├── gateway_server_simple.js  # 简化版服务器（新）
├── gateway_server.js         # 原始复杂版服务器
├── conf.json                 # 配置文件（与原版完全兼容）
├── package.json              # 项目配置
└── README.md                 # 说明文档
```

## 配置文件说明

配置文件 `conf.json` 完全兼容原版，包含以下主要部分：

### 1. models
定义每个模型的配置名称和使用范围：
```json
{
  "models": {
    "gpt-4o": {
      "config-name": "common-config-2",
      "use-in": ["gateway", "xdk"],
      "type": ["chat"]
    }
  }
}
```

### 2. configurations
定义具体的配置策略和目标：
```json
{
  "configurations": {
    "common-config-2": {
      "strategy": { "mode": "fallback" },
      "targets": [...]
    }
  }
}
```

### 3. providers
定义各种提供商的配置：
```json
{
  "providers": {
    "github": {
      "api_key": "...",
      "base_url": "..."
    }
  }
}
```

### 4. modelsmap
模型名称映射：
```json
{
  "modelsmap": {
    "gpt-4o-safe": "gpt-4o"
  }
}
```

### 5. gateway_paths
支持的网关路径：
```json
{
  "gateway_paths": ["gateway", "test", "xdk"]
}
```

## 使用方法

### 1. 安装依赖
```bash
npm install
```

### 2. 启动简化版服务器
```bash
npm start
# 或
npm run dev
```

### 3. 启动原始版服务器（对比用）
```bash
npm run original
```

### 4. API 调用示例

#### 获取模型列表
```bash
curl -H "Authorization: Bearer a881018" \
     http://localhost:3000/gateway/v1/models
```

#### 聊天完成
```bash
curl -X POST \
     -H "Authorization: Bearer a881018" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "gpt-4o",
       "messages": [
         {"role": "user", "content": "Hello!"}
       ],
       "stream": true
     }' \
     http://localhost:3000/gateway/v1/chat/completions
```

## 认证

服务器使用 Bearer token 认证，支持的 token：
- `a881018` (key-1)
- `a881018dddd` (key-2)

## 端口配置

默认端口：3000
可通过环境变量 `PORT` 修改：
```bash
PORT=8080 npm start
```

## 日志输出

简化版只输出关键信息：
- 请求的 key、模型和客户端 IP
- 模型映射信息
- 网关请求 URL
- 响应状态

## 错误处理

- 401: 未授权（无效的 Bearer token）
- 400: 缺少必需参数（如 model）
- 503: 模型未注册或配置未找到
- 500: 内部服务器错误或网关请求失败

## 与原版的区别

| 功能 | 原版 | 简化版 |
|------|------|--------|
| 基础转发 | ✅ | ✅ |
| 配置文件解析 | ✅ | ✅ |
| 模型映射 | ✅ | ✅ |
| 认证 | ✅ | ✅ |
| 搜索功能 | ✅ | ❌ |
| 思考模式 | ✅ | ❌ |
| 文件上传 | ✅ | ❌ |
| 复杂日志 | ✅ | ❌ |
| 内容转换 | ✅ | ❌ |
| 代码行数 | ~1600+ | ~250 |

简化版专注于核心的模型请求转发功能，代码更简洁、更容易维护。
