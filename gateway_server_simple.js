const express = require('express');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

const app = express();

// 添加 CORS 中间件
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  // 处理 OPTIONS 请求
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});

// 增加请求体大小限制
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 配置常量
const gatewayBaseUrl = 'https://gateway.588886.xyz';
const gatewayKeys = {
  'key-1': 'a881018',
  'key-2': 'a881018dddd'
  // Add more gateway keys here
};

const confPath = '/root/test/gateway/conf.json';

// 获取当前时间戳
function getCurrentTimestamp() {
    return Math.floor(Date.now() / 1000);
}

// 动态创建路由处理器
function createGatewayHandler(gatewayPath) {
  return async (req, res) => {
    const authHeader = req.headers['authorization'];
    const clientIp = req.headers['x-forwarded-for'] || req.ip;

    // 验证授权
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.split(' ')[1];
    const gatewayKeyName = Object.keys(gatewayKeys).find(key => gatewayKeys[key] === token);

    if (!gatewayKeyName) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // 处理模型列表请求
    if (req.path === `/${gatewayPath}/v1/models`) {
      try {
        const confContent = fs.readFileSync(confPath, 'utf8');
        const jsonData = JSON.parse(confContent);
        
        // 提取 models 部分
        const models = jsonData.models || {};
        const currentTime = getCurrentTimestamp();

        // 格式化模型数据，根据 use-in 参数和 type 过滤
        const formattedModels = Object.entries(models)
          .filter(([_, modelConfig]) => {
            // 检查是否是新格式
            if (typeof modelConfig === 'object') {
              // 检查 use-in 参数是否包含当前路径
              const useIn = modelConfig['use-in'] || ['gateway']; // 默认为 gateway
              const pathMatches = useIn.includes(gatewayPath);

              // 检查 type 是否包含 chat
              const typeMatches = modelConfig.type ? modelConfig.type.includes('chat') : true;

              return pathMatches && typeMatches;
            }
            // 旧格式全部保留（为了向后兼容）
            return true;
          })
          .map(([modelName, _]) => ({
            id: modelName,
            object: 'model',
            created: currentTime,
            owned_by: 'system'
          }));

        // 返回模型信息
        return res.json({ data: formattedModels });
      } catch (error) {
        console.error('Error reading or parsing config file:', error);
        return res.status(500).json({ error: 'Failed to read configuration file' });
      }
    }

    // 处理聊天完成请求
    const model = req.body.model;
    
    if (!model) {
      return res.status(400).json({ error: 'Model is required' });
    }

    console.log(`Request Key: ${gatewayKeyName}, Request model: ${model}, From: ${clientIp}`);

    let config;
    let mappedModel = model;
    
    try {
      const confContent = fs.readFileSync(confPath, 'utf8');
      const conf = JSON.parse(confContent);

      // 检查模型映射
      if (conf.modelsmap && conf.modelsmap[model]) {
        mappedModel = conf.modelsmap[model];
        console.log(`Mapped model to: ${mappedModel}`);
      }

      // 获取模型配置
      const modelConfig = conf.models[model];
      if (!modelConfig) {
        return res.status(503).json({ error: 'Model not registered' });
      }

      const configName = modelConfig["config-name"] || modelConfig;
      if (!configName) {
        return res.status(503).json({ error: 'Model configuration not found' });
      }

      config = conf.configurations[configName];
      if (!config) {
        return res.status(503).json({ error: 'Configuration not found' });
      }

      // 解析配置引用
      if (typeof config === 'string') {
        config = conf.providers[config];
      }

      // 递归解析 targets - 完全按照原始代码的逻辑
      const resolveTargets = (targetsOrProvider) => {
        if (typeof targetsOrProvider === 'string' && conf.providers[targetsOrProvider]) {
          return conf.providers[targetsOrProvider];  // 解析直接引用的 provider
        }

        if (Array.isArray(targetsOrProvider)) {
          return targetsOrProvider.map(target => {
            if (typeof target === 'string' && conf.providers[target]) {
              return conf.providers[target]; // 解析数组中的 provider 引用
            } else if (target.base_provider && conf.providers[target.base_provider]) {
              // 如果有 base_provider，则获取基础 provider 配置，并与 added_params 合并
              let resolvedProvider = Object.assign({}, conf.providers[target.base_provider], target.added_params);
              return resolvedProvider;
            } else if (target.targets) {
              target.targets = resolveTargets(target.targets);  // 递归处理嵌套的 targets
            }
            return target;
          });
        }

        // 处理对象形式的 base_provider 配置
        if (targetsOrProvider.base_provider && conf.providers[targetsOrProvider.base_provider]) {
          // 合并 base_provider 和 added_params
          return Object.assign({}, conf.providers[targetsOrProvider.base_provider], targetsOrProvider.added_params);
        }

        return targetsOrProvider; // 返回原始配置（如果没有需要解析的部分）
      };

      // 合并 base_provider 和 config 中的额外参数
      const mergeProviderConfig = (baseProviderKey, additionalConfig) => {
        if (!conf.providers[baseProviderKey]) {
          return additionalConfig;
        }

        // 创建基础配置的副本
        const result = { ...conf.providers[baseProviderKey] };

        // 创建additionalConfig的副本，以免修改原始对象
        const configCopy = { ...additionalConfig };

        // 如果存在added_params，将其内容复制到顶层
        if (configCopy.added_params) {
          // 将added_params中的所有内容复制到顶层
          Object.assign(result, configCopy.added_params);
        }

        // 最后合并剩余的配置（包括保留原始的added_params）
        Object.assign(result, configCopy);

        return result;
      };

      // 检查 config，如果是字符串（如 common-config-10），则解析 provider
      if (typeof config === 'string') {
        config = conf.providers[config]; // 直接将 provider 名称替换为完整的配置
      }

      // 如果有 base_provider 则进行合并
      if (config.base_provider) {
        config = mergeProviderConfig(config.base_provider, config);
      }

      // 处理 targets（如果有）
      if (config.targets) {
        config.targets = resolveTargets(config.targets);
      }

      // 配置解析完成
      console.log(`Configuration resolved for model: ${model}`);

    } catch (err) {
      console.error('Error reading or parsing config file:', err);
      return res.status(500).json({ error: 'Internal server error' });
    }

    // 构建请求
    const requestPath = req.path.replace(`/${gatewayPath}`, '');
    const gatewayUrl = `${gatewayBaseUrl}${requestPath}`;
    
    console.log(`Gateway request URL: ${gatewayUrl}`);

    try {
      const requestData = { ...req.body, model: mappedModel };
      
      const headers = {
        'x-portkey-config': JSON.stringify(config),
        'Content-Type': 'application/json'
      };

      console.log(`Sending request to gateway for model: ${mappedModel}`);

      const response = await axios({
        method: req.method,
        url: gatewayUrl,
        headers: headers,
        data: requestData,
        responseType: 'stream',
        timeout: 120000,
        validateStatus: function (status) {
          return status < 600;
        }
      });

      console.log(`Response received - Status: ${response.status}`);

      // 设置响应头
      if (!res.headersSent) {
        res.writeHead(response.status, response.headers);
      }

      // 转发响应流
      response.data.pipe(res);

    } catch (error) {
      console.error('Error forwarding request:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Gateway request failed' });
      }
    }
  };
}

// 读取配置文件获取网关路径
let gatewayPaths = ['gateway']; // 默认路径
try {
  const confContent = fs.readFileSync(confPath, 'utf8');
  const conf = JSON.parse(confContent);
  if (conf.gateway_paths && Array.isArray(conf.gateway_paths)) {
    gatewayPaths = conf.gateway_paths;
  }
} catch (err) {
  console.warn('Could not read gateway paths from config, using default:', err.message);
}

// 为每个网关路径创建路由
gatewayPaths.forEach(gatewayPath => {
  const handler = createGatewayHandler(gatewayPath);
  
  // 模型列表路由
  app.get(`/${gatewayPath}/v1/models`, handler);
  
  // 聊天完成路由
  app.post(`/${gatewayPath}/v1/chat/completions`, handler);
  
  // 其他可能的路由
  app.all(`/${gatewayPath}/*`, handler);
  
  console.log(`Registered routes for gateway path: ${gatewayPath}`);
});

// 启动服务器
const PORT = process.env.PORT || 3473;
app.listen(PORT, () => {
  console.log(`Gateway server running on port ${PORT}`);
  console.log(`Available gateway paths: ${gatewayPaths.join(', ')}`);
});
